
     Inserting image... 

User Stories 

Cycle Vita App – Caregiver Access 

Prepared For: Cycle Pharma 

Version v1.4  
Last updated: 15th September 2025 

 

 

 

 

 

 

 

 

 

 

 

Table of Contents 

​​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​ 
​​ 
 

 

 

 

 

 

[CG-001] Invitation Architecture Setup 
As a Patient, Caregiver, or HCP, 
 I want the app to support a secure and validated invitation mechanism, 
 so that I can invite, accept, or cancel invitations reliably with proper backend and frontend flows in place. 

Acceptance Criteria: 

Token Management 
Generate unique, secure tokens. 
Store and validate tokens. 
Status Lifecycle 
Support status changes: Pending → Accepted, Cancelled, Expired.. 
APIs & Reflections 
Provide endpoints to create, validate, and fetch invite statuses. 
Ensure inviter can see status updates. 
SMS Integration 
Send invites with deep links via SMS. 
Integration with SMS gateway for sending invites 
Handle retries, delivery logs, and localization. 
Security & Errors 
Return clear error codes for invalid/expired tokens 
[CG-002] Invite a Caregiver/HCP 
As an owner of the account, 
 I want to invite Caregiver/HCP using their phone number so that they can access my full data.  

Acceptance Criteria: 

Phone Number Entry & Validation 
As a primary owner, when I navigate to Menu -> Caregivers. 
And I click on Add on the top right corner. 
A bottom sheet is displayed on the screen. 
This bottom sheet consists of the following details: 
Name (input field) 
Placeholder text: “Enter name here” 
Role (single selection) 
Phone Number (input field) 
Placeholder text: “Enter phone number” 
Also invite for option with patient listing.  
Selecting this sends an invite for other patients Aswell 
Only those patients appear here for whom the Caregiver is also assigned as the primary owner. 
Note: In the case of the patient being the Primary Owner, this will not be displayed. 
A “Send Invitation” Button 
System validates the phone number format. 
Once the Send Invitation Button is clicked, a text message invite is shared. 
Once sent user return back to the previous screen. 
The sent invitation is added to the Pending Listing. 
Token-Based Invite 
The system generates a unique token for each invite. 
Invite Acceptance Flow 
Phone Number is sent without storing it in the database. 
When the invited Caregiver/HCP clicks Accept on the web UI and completes signup: 
The token is validated. 
A user account is created only if one does not already exist. 
The new Caregiver/HCP is linked to the inviting patient. 
This Caregiver/HCP is added to the Caregivers Listing. 
Handling Existing Users 
If the phone number already exists: 
Any changes made should be reflected in the user account. 
Security 
Tokens expire after a set duration (e.g., 7 days). 
Tokens are valid for one-time only. 
[CG-003] Invite a Patient 
As a Caregiver, I want to invite patients using their phone numbers so that I can access their data and make necessary changes. 

Acceptance Criteria: 

Phone Number Entry & Validation 
Caregivers can enter a patient's phone number to send an invitation. 
This will be done in the Manage Profile Section. 
Caregivers can click on the Manage Profile Button in the profile switcher bottom sheet. 
Once clicked the Caregiver will see an Add Profile Button. 
Clicking on it takes the Caregiver to the Profile screen. 
Caregivers must insert all the information to create a patient record (excluding the invite section).i. The caregiver should enter all information for the patient record; the patient will be able to amend when they have access to their profile. The allowance history should include what user changed the allowance. 
The only mandatory field is Phe allowance. 
Time zone is auto filled with the creating user’s time Zone. 
By default, all toggles are off. 
Track my consumption is by default “Phe”. 
Body Description Invite This Profile to create their Own Account Section: 
“Enter a mobile number below to invite this profile  to create their own account. They’ll be able to manage their own treatment and access the  app independently. Once the invitation is sent, they’ll receive a text message with instructions  to get started.” 
To create a user account that the Caregiver must also include a phone number for the patient to create their own account. 
Clicking on save after providing all the details along with the phone number the system should validate the phone number and send an invite. 
Patient Record Creation: (without phone number) 
The Caregiver/HCP can add the following details to create a patient record: 

Name  
Profile Picture 
Track Consumption Using (Phe/Protein) 
Phe Allowance  
Simplified Diet (toggle) 
Push Notifications (toggle) 
Clicking on “Save” creates a patient record (not a user account) and is linked with the Caregiver/HCP. 

This user is reflected on the patient switcher listing & on the Manage Profile Listing. 

Invite a Patient to Create Their Own Account: 
To invite a patient to create their own account, the Caregiver/HCP must insert a valid phone number in this section on the Profile. 
Body Description:  
“ Enter a mobile number below to invite this profile to create their own account. They’ll be able manage their own treatment and access the app independently. Once the invitation is sent, they’ll receive a text message with instructions to get started.” 
 Input Label: Phone Number 
Input field 
Placeholder text: Enter phone number. 
The user must also fill out the Name, Profile Picture, Track Consumption Using (Phe/Protein), Phe Allowance, Simplified Diet (Toggle), & Push Notification (toggle). 

Clicking on send allows user to send an invitation on the phone number. 
Invitation text (TBC) 
The section is updated with Invite Status as (Sent). 
Option to Resend & Cancel Invite are enabled. 
This user is now added to the Manage Profile Listing with a yellow indicator. 
The system validates the phone number format. 
 
Phone Number is not stored in the database until the invite is accepted. 
Token-Based Invite Generation 
A secure, unique token is generated and attached to the invite. 
A text message is sent to the patient with a deep-linked sign-up URL containing the token. 
Token links the future patient account to the inviting Caregiver/HCP. 
Invite Acceptance Flow 
When the invited patient clicks Accepts and signs up: 
Token is validated. 
A patient user account is created (if not already existing). 
The Caregiver/HCP is linked to the patient. 
Patients are granted access to their own data. 
Handling Existing Users 
If the phone number already exists: 
The user will go through the same invitation process as a new user. 
The system links the existing patient account to the Caregiver/HCP. 
No duplicate user is created in the database. 
Security 
Tokens are single-use and expire (e.g., after 7 days). 
Token data is stored securely and deleted after use or expiration. 
[CG-004] Manage Patient Profiles 
As a Caregiver/HCP, I want to manage patient profiles i.e. I can add, view or remove patients. 

Acceptance Criteria: 

Patient Listing 
Caregiver/HCPs can click on the Manage profile button to view the Manage Profile Screen. 
This Screen consists of the Patient Profiles that a caregiver has added or have invited.. 
Each patient entry includes: 
Name 
Profile Picture 
Invitation State 
Yellow Indicates Sent 
Green Indicates Accepted. 
Grey indicates Created not invited. 
Clicking on a patient opens the patient Profile. 
 
Patient Profile 
Caregiver/HCPs can click on any patient in the listing and open their profile. 
Each Patient Profile includes the following details: 
Profile Picture 
Name 
Track Consumption using (Phe or Protein) 
Phe Allowance  
Simplified Diet (toggle) 
Push Notifications (toggle) 
Send Invite. 
From the Patient Profile screen: 
Caregiver/HCPs can click "Remove" to unlink the patient. 
The patient can still access their own data. 
The Caregiver/HCP cannot access the account again unless re-linked by the patient. 
Caregiver/HCPs can click "Save" to save any changes made to the patient profile. 

Post-Removal Handling 
If a Caregiver/HCP has no remaining patients: 
The Caregiver/HCP is redirected back to the Manage Profile Screen. 
UI Requirements 
The Manage Patients screen is accessible from the Caregiver/HCP profile. 
The screen contains: 
Manage Profiles Listing 
Add Profile Button 
Security & Permissions 
Only the Caregiver/HCP can manage their patient list. 
Removal does not affect the patient’s own access or other Caregiver/HCPs’ access. 
Empty State	 
If no patients are listed (Due to being onboarded or having no patients), the following message is displayed: 
“You don’t have any patients at the moment. 
 Add your first patient using the button below.” 
The Add Profile Button is shown. 
The Continue button remains disabled until at least one profile is added. 
[CG-005] Access Patient Profile 
As a Caregiver/HCP, I want full read/write access to each of the patients that i manage 
 
Acceptance Criteria: 

Access Conditions 
Caregiver/HCPs can only access data for patients they are currently linked to. 
Read Access 
Caregiver/HCP can view: 

All data (In case of Primary Owner) 
All data excluding making primary or removing caregiver (In case of Non-Primary Owners) 
Write Access 
Caregiver/HCP can: 
All data (In case of Primary Owner) 
All data excluding making primary or removing caregiver (In case of Non-Primary Owners) 
Multi-Patient Management 
Caregiver/HCPs can easily switch between multiple patient profiles. 
A caregiver can access one patient at a time, but multiple caregivers can also access one patient at a time on their devices. 
Revocation Handling 
If Caregiver/HCP’s access is removed: 
They can no longer view or edit the patient’s data. 
The patient is removed from their patient list immediately. 
Only primary owner can remove a caregiver. 
UI displays a modal that takes the user to the Manage Profiles Screen. 
And the removed caregiver should be notified via email. Email text (TBC) 
Security 
All actions are authorized per Caregiver/HCP-patient link. 
[CG-006] Resend or Cancel Invitation & Invitation Tracking 
As a Caregiver, 
 I want to see the status of the invitations I have sent and be able to cancel or resend them, 
 so that I can manage pending invitations and know when they are accepted. 

Acceptance Criteria: 

Invite State Display (Profile > Invite to Create Own Account Section) 
When clicked on any profile in the Manage Profile and navigating to the Create Own Account Section. 
The following status indicators should appear in case an invite has been sent. 
Status Indicators: 
Yellow Dot = Pending (Invite Sent) 
The user must be able to see the Resend or cancel Invite for this invitation state. 
Green Dot = Accepted (Invite Accepted) 
Grey Dot = Created but not invited. 
Expired (Invite older than 7 days, token invalid) (TBC) 
Resend: 
Users can click on Resend only when the shared invite has expired or has been rejected. 
Clicking on Resend generates a new token 
Send a new invite with a token expiry of 7 days. 
Resend button remains disable till the previous token expires. 
Cancel: 
Users can click on Cancel only when an invite has been shared already. 
Clicking on cancel Invalidates the invitation token. 
Once the invitation is canceled, the user should be able to resend an invite with a new token. 
A prompt is displayed: 
Patient Profile 
Patient Name 
Body Description: 
“Are you sure want to cancel this invitation?” 
Options to select: 
Yes, cancel Invitation (This cancels the invite) 
Clicking on yes closes the modal and the invite is canceled. 
No, I changed my mind. 
This closes the modal and aborts the process. 
 

Cancelled or expired invites cannot be used for sign-up 
The token expires immediately. 
[CG-007] Handle Expired Invitations (Web View) 
As a user, 

 I want expired invitations to be marked clearly, 

 So that I know which invites are no longer valid. 

Acceptance Criteria: 

If the invited user tries to access the invitation link after 7 days the webview should display a message “Invitation Expired” 
Invite tokens have a configurable expiry time (e.g., 7 days). 
Invites status show with status “Expired”. 
Expired invites cannot be used to create accounts. 
User has the option to resend an invite (new token generated). 
This screen consists of the following information: 
Header: This invitation has expired 
Body Description: “You don’t need an invitation to use Cycle Vita PKU. Simply download the app using one of the links below.” 
App Links: Both Android & Apple 
 

[CG-008] Log Food for One or More Patients 
As a Caregiver, 
 I want to log food for one or more patients at the same time, 
 and if a patient has Simplified Diet enabled, I want the option to mark food as “free,” 
 so that I can accurately track diet intake for multiple patients. 

Acceptance Criteria: 

Access Diet Tracker 
Caregivers/HCPs can navigate to the Diet Tracker from any patient profile. 
On the Log Food screen, an option to select multiple patients is available. 
Patient Selection List 
A list of all patients linked to the Caregiver is displayed with: 
Patient Name 
Profile Image 
A checkbox next to each name 
Upon selection a conditional checkbox for “count as free food”. 
Caregivers can select one or more patients by checking the boxes. 

 

 
Default Selection Behavior 
If the Caregiver/HCP accessed the log screen from a specific patient profile: 
Caregiver/HCPs can select or unselect additional patients from the section below. 
Simplified Diet Handling 
For patients with Simplified Diet enabled: 
A checkbox labeled “Count as free food” is displayed. 
Caregiver/HCP can tick/untick per patient. 
If unchecked → food logs with full phe/protein values. 
If checked, → food is logged as free for that patient only. 
For patients without Simplified Diet enabled: no free food checkbox is shown. 
Edge Cases 
If a Caregiver/HCP has only one patient: 
The checkbox list is not shown (auto-logs for that patient). 
If a patient has been removed or removes the Caregiver/HCP during the process: 
The system alerts the Caregiver/HCP or patient via email and deselects the invalid patient before submission. 
Free food logging applies per patient (one patient may count food as free, others may not). 
Note: This is only applicable when adding foods via search or manually not for the scan meal. 

c. Security & Access Control 

Only Caregiver/HCPs with access to the patients can perform this action. 
[CG-009] Refer a Friend via SMS 
As a user (Patient, Caregiver, HCP),  

 I want to go to the hamburger menu and refer a friend  

 So that I can send a unique URL as a referral via SMS. 

Acceptance Criteria: 

Refer a Friend Option in Menu 
The “Refer a Friend” option is accessible from the hamburger menu. 
An input field for a phone number is available where the user can: 
Enter the friend’s phone number. 
Initiate SMS Invite 
Upon submission: 
A unique referral URL is generated for the invitee. 
An SMS is sent to the provided phone number containing: 
The referral message. 
The unique sign-up URL. 
Web-Based Onboarding Flow 
The referral URL directs the invitee to a web-based sign-up page. 

Referral Tracking 
The referral link is unique to the inviter. 

Security & Validations 
Phone number input is validated in the correct format before sending SMS. 
[CG-010] Last Logged Patient 
As a Caregiver/HCP, 
 When I log into the app, 
 I want to automatically access the last patient I was viewing before I close the app, 
 so that I can seamlessly continue managing their data without manually selecting them again. 

Acceptance Criteria: 

Store Last Active Patient 
The system records the last patient profile the Caregiver/HCP viewed before app termination. 
This information is stored securely in the Caregiver/HCP account. 
Auto-Load on Login 
Upon successful login, if the Caregiver/HCP has multiple patients: 
The app displays the patient's listing. 
The Caregiver/HCP is directed to the first patient in the listing) 
First-Time Login Case 
If it's the first-time login: 
The Caregiver/HCP is directed to the patient listing screen to add a patient screen (first onboarding screen). 
Single Patient Case 
If the Caregiver/HCP has only one single patient added, then the Caregiver/HCP will always be directed to the dashboard on every login of that patient. 
No user dropdown to be displayed on each screen except for the menu. 
Access Revoked Scenario 
If the last patient was removed or access is revoked: 
The Caregiver/HCP is directed to the patient listing screen. 
Switching Patients 
Switching to a different patient updates the “last active patient” record. 
Next login will default to this newly accessed patient. 
UI/UX 
Transition should feel seamless; no splash screen or manual confirmation needed. 
If loading fails, a fallback to patient selection screen is shown gracefully. 
Security 
If a Caregiver/HCP has no patients, they are prompted to add a patient. 
[CG-011] Caregiver/HCP Access Revocation 
As a primary owner, 
 When I remove a Caregiver/HCP , 
 I want their access to my data to be revoked, when they try using the app, 
so that I stay in control of my privacy and data security. 

Acceptance Criteria 

 Access Revocation 
When a primary owner removes a Caregiver/HCP from their profile: 
The Caregiver/HCP’s access token/session for that patient is invalidated instantly. 
The system removes the Caregiver/HCP-patient link from the database. 
If the Caregiver/HCP is actively using the app: 
The Caregiver/HCP is redirected to: 
The Manage Profile screen. 
In case of no patients, the caregiver must add a patient to continue using the application. 
An email notification to be sent to the caregiver about losing access and they should be redirected to the manage profile screen. 
Security Handling 
Session tokens or cached data for the removed patient are cleared from the Caregiver/HCP’s device storage. 
Edge Cases 
If the Caregiver/HCP is offline: 
Upon the next app sync or data fetch, access revocation is enforced. 
Cached patient data is cleared immediately upon reconnect. 
[CG-012] HCP Access 
As an HCP 

When I log into the application 

I want the same permissions and access as a Caregiver. 

Acceptance Criteria 

Role Assignment 
During onboarding, if a user selects HCP, their role is set as HCP in the system. 
Same access control as a Caregiver role. 
Access Scope 
HCPs can: 
Add and manage other patients. 
HCPs can only access their linked patient data. 
HCPs cannot manage other Caregiver/HCPs (Unless they are not a primary owner) 
They can be invited by the primary owner.  
UI Behavior 
App UI behaves the same for HCPs as it does for caregivers. 
[CG-013] Web UI Architecture  
As a development team, 
 I want to establish the necessary front-end and back-end setup for rendering and integrating web screens, 
 so that the screens function correctly and securely. 

Acceptance Criteria: 

Multi-theme Support 
Implement light/dark theme switcher. 
Ensure theme persistence across sessions. 
Authentication (Auth0 Integration) 
Configure login/logout flows with Auth0. 
Handle token storage and refresh securely. 
Routing 
Set up routes. 
Handle redirects for unauthorized users. 
API Integration 
State Management 
Handle user, session, and invite states (pending, accepted, cancelled, expired) 
CORS Handling 
Configure frontend to manage cross-origin requests. 
Form Validations 
handle validations. 
Consistent error messages across forms. 
Styling & Responsiveness 
Base layout grid. 
Mobile-first responsive design. 
Animations 
Translations (i18n) 
Multiple ENV Handling 
Config setup for dev, staging, and production. 
API endpoints and keys per environment. 
FE Build Pipelines & Repo Setup 
Gilt repo initialization with branching strategy. 
CI/CD pipeline for builds and deployments. 
Pipeline Setup 
CORS Handling 
Subdomain Configuration 
[CG-014] Patient/Caregiver/HCP Web Onboarding  
As a Patient, Caregiver, or HCP, 
 I want to send an invite to another role, 
 so that the recipient can open it in the Web UI, accept, and be linked to me with the correct role auto assigned. 

Acceptance Criteria 

Invite Delivery 
Invite sent via SMS using Twilio. 
Invite contains a unique Web URL with token valid for 7 days. 
If token is expired → recipient sees “This invite has expired” screen (explicit message). 
Heading: This invitation has expired 
Body Description: “You don’t need an invitation to use Cycle Vita PKU. Simply download the app using one of the links below.” 
App Links are added as buttons. 
 

If token already redeemed → recipient sees “This invite has already been used.” (design pending) 
If token invalid/tampered → recipient sees error screen. (design) 
 
Web UI Flow for Role-Based Invites (Patient → Caregiver/HCP, Caregiver/HCP → Patient) 
Users click on the URL received on their phone number. 
Step 1: You’ve Been Invited screen 

 

Heading: “You’ve been invited ...” 
Body Description: TBC 
Accept Invitation Button: Directs Users to the next step screen 
A counter at the bottom is displayed where x denotes total steps and the numerator denotes the current step. 
Counter Text: Begin Your Cycle Vita Journey 1/x 
 

Step 2: Disclaimer screen  

 

Heading: “Disclaimer” 
Body Description:  
“The information provided through this application, including any medical or health-related content, is intended for informational and educational purposes only. It is not intended as a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of a qualified healthcare provider for any questions you may have regarding a medical condition. Never disregard professional medical advice or delay in seeking it because of something you have read or accessed through this application.” 
I Understand Button: Directs Users to the next step screen 
A counter at the bottom is displayed where x denotes total steps and the numerator denotes the current step. 
Counter Text: We’re so excited to have you here 2/x 
 
Step 3: T&C 

 

Heading: “T&C” 
Body Description:  
“ 
  I have read and agree to the terms & conditions. 
 
 Sign up to be contacted with relevant information and updates, including new features and product insight. 
 
 I agree to the use of my data for user insights and improvements to Cycle’s products and services.” 
Continue Button: Directs Users to the next step screen 
Counter Text: But first, some housekeeping 3/x 
It is mandatory for users to select the first checkbox to proceed. 
Role Auto-Assigned → recipient does not see role selection, survey, or user detail screens. 
Step 4: Create Account Screen: 

 

Heading: “Create an Account” 
user signs up via Google, Apple, Microsoft, Facebook. 
 
Step 5: Confirm your Details Screen (Only Applicable for New Accounts) 

 

Heading:  Confirm your details 
Fields populated from the social logins: 
Profile Picture  
Name field 
Email field 
Phone Number field 
“I confirm” Button that directs user to the next screen. 
Counter Text: Just one more step 5/x 
These are editable for the user. 
Step 6: You Are All Set screen → shows App Store & Play Store links. 

If the Caregiver is a new user 
Heading: You’re all set! 
Body Description:  
“You are now a caregiver for [Patient name]!  
All you have to do next is download the app....” 
App Links are added as buttons. 
If the Caregiver already has an account 
Heading: You’re all set! 
Body Description: 
 “ You are now a caregiver for [Patient name]!  It looks like you already have an account.  To access this please download the app....." 
App links are added as buttons. 
 

 

 

If user has already downloaded app → clicking link opens the app directly. 
Linking Behavior 
Once signup is complete: 
Patient and Caregiver are linked. 
Patient and HCP are linked. 
Caregiver & HCP have full access to Patient’s profile (view and manage data except for Caregivers listing (not applicable in case of Primary Owner)). 
Mobile Responsiveness 
The Web designs should be responsive for all mobile devices. 

[CG-015] Refer a Friend Onboarding 
As a Patient, Caregiver, or HCP, 
 I want to refer a friend by sending them an invite, 
 so that they can open it in the and complete the full onboarding process. 

Acceptance Criteria 

Invite Delivery 
Invite sent via SMS using Twilio. 
Invite contains a unique Web URL . 
If token already redeemed → recipient sees “This invite has already been used.” 
If token invalid/tampered → recipient sees error screen. 
Web UI Flow for Referral Invites 
You’ve Been Invited screen → user clicks Accept Invitation. 
 

Disclaimer screen → user clicks I Understand. (Same as existing screen). 
 

T&C Screen -> user clicks Continue 
 

Role Selection screen → choose Patient / Caregiver/ HCP and click on Continue. (Same as existing screen). 
 

If User Chooses (Someone with PKU) 
User Survey screen → (Same as existing screen). 
 

Let's set up your profile screen → User clicks on Continue. 
Create Account screen (Auth0 Social Logins) (Same as existing screen). 
 

Confirm your details Screen -> Auto-populated details 
 

You Are All Set screen → shows App Store & Play Store links. 
 

If User Chooses (Caregiver or HCP) 
User Survey screen → (Same as existing screen). 
 

Sign up screen (Same as existing screen). 
 

Confirm your details Screen -> Auto-populate details from social login 
 

You Are All Set screen → shows App Store & Play Store links. 
 

 
If User Opens the App for the first time 
If the user opens the app for the first time, then they must first set their preference. 
 

Relationship Behavior 
Referral does not auto-link users. 
Friends join independently. 
Can later establish relationships via role-based invites. 
Mobile Responsiveness 
The Web designs should be responsive for all mobile devices. 

[CG-016] Push Notification for Invite Status 
As a Patient, Caregiver or HCP who sends an invite, 
 I want to receive push notifications, 
 so that I am always informed of the status of my sent invitations. 

Acceptance Criteria 

Invite Accepted 
When a recipient accepts an invite: 
The inviter receives a push notification: 
Title: “Your invite was accepted” 
Message: “[Recipient Name] has accepted your invitation.” 
The notification is sent immediately after acceptance. 
Invite Declined 
When a recipient declines an invite: 
The inviter receives a push notification: 
Title: “Your invite was declined” 
Message: “[Recipient Name] has declined your invitation.” 
General Behavior 
Notifications are only sent to the inviter (not the invitee). 
If the inviter has disabled push notifications, no alerts are delivered. 
[CG-015] Phe/PRO Allowance History Update  
As a Patient with a Caregiver/Caregivers, 
 I want to see what updates were made to the Phe/PRO allowance in the history log, 
 so that I can keep track of what was changed. 

Acceptance Criteria 

Navigation to Edit 
When a user clicks Edit on the Phe Allowance from the menu, 
 They should be redirected to the Phe/PRO Allowance Screen. 
Tracking Updates 
Each time the Phe allowance is updated, a new entry is added to the history table on the backend no changes on the frontend. 
Audit Integrity 
Updates remain visible even if the user who made them is later removed. 

[CG-016] Primary Owner 
As a Patient, 
 I want to designate one of my linked Caregivers or HCPs as my Primary Owner, 
 so that there is always a clear primary owner responsible for managing my data, tasks, and settings. 

Acceptance Criteria 

Definition of Primary Owner/HCP 
At all times, there must be one primary Owner. 
Only the Owner can: 
Access My Caregivers Listing. 
Assign/reassign ownership. 
Manage time zone settings. 
Ownership Rules 
Patient Sign-Up Without an Invitation (Self-Owned Account) 
Patient Signs Up or Logs in and is a user themselves they would be considered as a primary owner until unless they add a caregiver and make them a primary caregiver. Patients will be assigned as the default Owner. 
 

Patient Sign-Up via Invitation from Caregiver/HCP 
The invited patient becomes the Owner by default. 
The Owner can assign ownership to anyone from the Caregiver Listing, which revokes all the Owner privileges from them. 
Caregiver/HCP-Created Patient Record (No User Account) 
Caregiver/HCP is Owner by default. 
If the Patient later creates an account, ownership remains with Caregiver unless manually reassigned. 
Multiple Caregivers/HCPs 
Only one can be the Owner at a time. 
Owner Account Deletion 
If the Owner deletes their account and there are multiple caregivers: 
The system prompts the Owner to reassign ownership before deletion. 
This is a mandatory action to be taken to proceed further with account deletion. 
If only one Caregiver exists and deletes their account: 
Ownership transfers automatically to the Patient. 
Patient as Sole Owner 
If no caregivers are linked, Patient is always the Owner. 

[CG-017] Manage Caregivers  
As a primary Owner, I want to manage (view, remove) caregivers/HCPs so that I stay in control of who can access my data and view pending Caregiver/HCP requests. 

Acceptance Criteria: 

View Current Caregiver/HCPs 
In Menu > Caregivers: 
Screen header label: “Caregivers” 
Body Description: To be provided 
Back arrow at the top left corner. 
This arrow directs the user to the Menu. 
Add Button on the top right corner to add Caregivers. 
Option to “Make Primary” shown for non-primary owner. 
 

Upon Clicking the following modal is displayed: 
If user selects Yes, Remove Primary Status: 
The Primary Owner rights are provided to the selected Caregiver and removed from the existing Caregiver. 
This takes the user back to the Menu with Caregivers listing removed from their view. 
If user selects No, I changed my mind: 
Modal closes. 
User is taken back to the previous screen. 
 
Option to “Remove” Caregiver for all Caregivers in the listing. 
Once clicked a prompt is displayed “Are you sure you want to remove [Caregiver/HCP Name] as your caregiver?” 
If user selects “Yes remove caregiver” 
The caregiver’s access is revoked. 
Patients and caregivers are unlinked in the system. 
The caregiver receives a notification/email that their access has been revoked. (Email template to be shared) 
The caregiver is instantly removed from the patient’s caregiver listing. 
If the user selects “No I changed my mind” 
The prompt closes. 
No action is taken. 
The caregiver remains linked and visible in the caregiver list. 
 

 

The remove and make primary buttons are only enabled to the Primary Owner. 
A Section that displays “Pending” Caregivers is displayed on the screen. 
This displays all pending invites. 
Users add a caregiver via the Add Button, and the pending invite is displayed here. 
This invitation can be cancelled or resent (Refer to CG-005) for functionality. 
Empty state if no pending invites: 
Message: “There are no pending invitations yet.” 

 

d. The Primary Owner receives the time zone modal and takes relevant actions. These changes are reflected on the patient profile Aswell. 

[CG-018] Caregiver/HCP’s Profile 

As a Caregiver/HCP, When I access my profile, I should have the ability to view and edit my profile. 

Acceptance Criteria: 

View Profile 
Caregiver/HCPs can access a Profile screen from the menu by clicking on My profile tab. 
The profile screen displays the Caregiver/HCPs General Details: 
Name 
Phone number (if provided) 
Email address 
Delete Account Button with body description. 
UI Mode (Dark, Light or System) 
Edit Profile 
Caregiver/HCP can edit: 
Name: Free text input with validation (e.g., min 3 characters). 
Phone number: Optional field with formatting and validation (e.g., +*********). 
Email address: 
Not editable 
Marketing & Data Usage Checkboxes. 
Save Changes 
Changes can be saved by tapping “Save Changes” button. 
Show confirmation message on success (e.g., “Profile updated successfully”). 
Cancel Changes 
Option to discard unsaved changes and return to the last saved state by clicking on the arrow on the top left corner. 
[CG-019] Profile Switcher 
As a Caregiver, 
 I want to switch between patient profiles using a profile switcher, 
 so that I can quickly access and manage any of my linked patients in the app. 

Acceptance Criteria: 

Profile Switcher Placement 
A profile switcher is available at the top of each screen clicking on it  a bottom sheet appears on: 
Dashboard 
Labs 
Tasks 
Diet Tracker 
Menu  
This will be a tab called “Manage profile” which will take the user to the manage profile screen. 
Profile Listing 
The bottom sheet displays all linked and accessible patient profiles. 
Each profile entry includes: 
Name 
Profile Picture (if available) 
A progress bar displayed for the current day around each profile image. 
Only patients the Caregiver/HCP currently has access to are displayed. 
Switching Profiles 
Tapping a profile in the switcher immediately switches the context to that patient. 
The user is redirected to the Dashboard of the selected patient. 
Manage Profiles Access 
A “Manage Profiles” button is displayed at the bottom of the profile switcher. 
Tapping this button redirects the user to the Manage Patient Profiles screen (CG-003). 
Empty State / No Patient Scenario 
If no patients are linked: 
Caregiver is directed to the Manage Profiles, where they must add a profile to use the application. 
 

[CG-020] Time Zone Management 
As a Primary Owner, 

I should be able to decide for the time zone changes on the pop-up if the system detects the difference between the profile time and the device time so that i can make sure that i take my medications at the accurate time.  

Acceptance Criteria:  

Patient as a Primary Owner: 
If a patient is a primary owner, they should be the one receiving the pop-up if the changes are detected in the profile time and my device time.  
Caregiver/HCP as a Primary Owner: 
If a caregiver is a primary owner and their device time differs from the profile time of the patient that I am managing, then I should be receiving the pop-up however whatever I decide on the pop-up should be reflected to the relevant patient's dashboard and profile as well.  
Non – Primary Owner:  
If either caregiver or a patient is not a primary caregiver, they should only be able to make changes manually in the profile time or make changes to the toggle of following home time but should not receive a pop-up. 